### All Submissions:

* [ ] Does your code follow [WooCommerce](https://docs.woocommerce.com/document/create-a-plugin/) and [WordPress](https://make.wordpress.org/core/handbook/best-practices/coding-standards/) standards?
* [ ] Have you successfully run tests with your changes locally?
* [ ] Have you tested both blocks/non-blocks cart/checkout?
* [ ] Have you tested the cart and checkout as both a logged-in user and a guest?
* [ ] Have you successfully placed an order as both a logged-in user and a guest?
* [ ] Did you have the query monitor plugin active during all testing?



<!-- <PERSON> completed items with an [x] -->

<!-- You can erase any parts of this template not applicable to your Pull Request. -->

### Changes proposed in this Pull Request:

<!-- Describe the changes made to this Pull Request and the reason for such changes. -->

Closes # .

### How to test the changes in this Pull Request:

1.
2.
3.

### Other information:

* [ ] Have you checked to ensure there aren't other open [Pull Requests](../../pulls) for the same update/change?

<!-- Mark completed items with an [x] -->

### Changelog entry

> Enter a summary of all changes on this Pull Request. This will appear in the changelog if accepted.
