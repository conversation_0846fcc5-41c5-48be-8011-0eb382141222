# Copyright (C) 2025 PostNL
# This file is distributed under the GPLv2 or later.
msgid ""
msgstr ""
"Project-Id-Version: PostNL for WooCommerce 5.8.0\n"
"Report-Msgid-Bugs-To: https://wordpress.org/support/plugin/postnl-for-woocommerce\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"POT-Creation-Date: 2025-09-08T20:41:12+00:00\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"X-Generator: WP-CLI 2.11.0\n"
"X-Domain: postnl-for-woocommerce\n"

#. Plugin Name of the plugin
#: postnl-for-woocommerce.php
msgid "PostNL for WooCommerce"
msgstr ""

#. Plugin URI of the plugin
#: postnl-for-woocommerce.php
msgid "https://github.com/Progressus-io/postnl-for-woocommerce/"
msgstr ""

#. Description of the plugin
#: postnl-for-woocommerce.php
msgid "The official PostNL plugin allows you to automate your e-commerce order process. Covering shipping services from PostNL Netherlands and Belgium."
msgstr ""

#. Author of the plugin
#: postnl-for-woocommerce.php
msgid "PostNL"
msgstr ""

#. Author URI of the plugin
#: postnl-for-woocommerce.php
msgid "https://postnl.post/"
msgstr ""

#: src/Admin/Survey.php:41
msgid "PostNL Survey"
msgstr ""

#: src/Admin/Survey.php:114
#: src/Admin/Survey.php:158
msgid "Would you like a chance to win a Bol gift card worth €25?"
msgstr ""

#: src/Admin/Survey.php:115
#: src/Admin/Survey.php:161
msgid "Let us know what you think of the PostNL for WooCommerce plugin by completing the survey."
msgstr ""

#: src/Admin/Survey.php:121
msgid "Take Survey"
msgstr ""

#: src/Admin/Survey.php:128
#: src/Admin/Survey.php:175
#: src/Main.php:475
msgid "Leave a review"
msgstr ""

#: src/Admin/Survey.php:168
msgid "Take survey"
msgstr ""

#: src/Checkout_Blocks/Extend_Block_Core.php:84
#: src/Frontend/Container.php:336
msgid "This is not a valid address!"
msgstr ""

#: src/Checkout_Blocks/Extend_Block_Core.php:99
#: src/Frontend/Checkout_Fields.php:64
#: src/Frontend/Checkout_Fields.php:65
#: src/Order/Single.php:533
msgid "House number"
msgstr ""

#: src/Checkout_Blocks/Extend_Block_Core.php:158
msgid "PostNL Delivery Fee"
msgstr ""

#: src/Checkout_Blocks/Extend_Block_Core.php:160
#: src/Frontend/Base.php:529
msgid "PostNL Morning Fee"
msgstr ""

#: src/Checkout_Blocks/Extend_Block_Core.php:162
#: src/Frontend/Base.php:510
msgid "PostNL Evening Fee"
msgstr ""

#: src/Checkout_Blocks/Extend_Block_Core.php:358
msgid "Postcode or house number is missing."
msgstr ""

#: src/Emails/WC_Email_Smart_Return.php:43
msgid "Smart Return Email"
msgstr ""

#: src/Emails/WC_Email_Smart_Return.php:44
msgid "This is a smart return email sent for return purposes."
msgstr ""

#: src/Emails/WC_Email_Smart_Return.php:62
msgid "[{site_title}]: PostNL Smart Returns"
msgstr ""

#: src/Emails/WC_Email_Smart_Return.php:72
msgid "PostNL Smart Returns"
msgstr ""

#: src/Frontend/Base.php:146
msgid "Pick up at PostNL-point"
msgstr ""

#: src/Frontend/Base.php:171
#: src/Order/Single.php:322
msgid "Date:"
msgstr ""

#: src/Frontend/Base.php:175
#: src/Order/Single.php:326
msgid "Time:"
msgstr ""

#. translators: %s: shop cart url
#: src/Frontend/Base.php:240
msgid "Sorry, your session has expired. <a href=\"%s\" class=\"wc-backward\">Return to shop</a>"
msgstr ""

#: src/Frontend/Checkout_Fields.php:101
#: src/Frontend/Checkout_Fields.php:102
#: src/Shipping_Method/Settings.php:244
msgid "House Number Extension"
msgstr ""

#: src/Frontend/Checkout_Fields.php:106
msgid "Street"
msgstr ""

#: src/Frontend/Checkout_Fields.php:107
msgid "Street Name"
msgstr ""

#: src/Frontend/Delivery_Day.php:45
msgid "Please choose the delivery day!"
msgstr ""

#: src/Frontend/Delivery_Day.php:115
#: src/Shipping_Method/Settings.php:322
#: build/postnl-container-frontend.js:1
#: src/Checkout_Blocks/js/postnl-container/block.js:94
msgid "Delivery"
msgstr ""

#: src/Frontend/Dropoff_Points.php:52
#: build/postnl-container-frontend.js:1
#: src/Checkout_Blocks/js/postnl-container/block.js:95
msgid "Pickup"
msgstr ""

#: src/Frontend/Fill_In_With_Postnl.php:220
msgid "Invalid or expired nonce."
msgstr ""

#: src/Frontend/Fill_In_With_Postnl.php:230
#: src/Frontend/Fill_In_With_Postnl_Handler.php:71
msgid "Fill in with PostNL is not enabled or Client ID is missing."
msgstr ""

#: src/Frontend/Fill_In_With_Postnl_Handler.php:68
msgid "Invalid nonce."
msgstr ""

#: src/Frontend/Fill_In_With_Postnl_Handler.php:76
msgid "No user data"
msgstr ""

#: src/Frontend/Fill_In_With_Postnl_Handler.php:109
msgid "Login session expired. Please try again."
msgstr ""

#. translators: %s is the error message from PostNL
#: src/Frontend/Fill_In_With_Postnl_Handler.php:133
msgid "PostNL Token Request Error: %s"
msgstr ""

#: src/Frontend/Fill_In_With_Postnl_Handler.php:146
msgid "PostNL: Access token not found"
msgstr ""

#. translators: %s is the error message from PostNL
#: src/Frontend/Fill_In_With_Postnl_Handler.php:165
msgid "PostNL User Info Error: %s"
msgstr ""

#: src/Frontend/Fill_In_With_Postnl_Handler.php:181
msgid "Incomplete user data."
msgstr ""

#: src/Helper/Mapping.php:30
#: src/Shipping_Method/Settings.php:353
msgid "Morning Delivery"
msgstr ""

#: src/Helper/Mapping.php:31
msgid "Standard Shipment"
msgstr ""

#: src/Helper/Mapping.php:32
#: src/Shipping_Method/Settings.php:371
msgid "Evening Delivery"
msgstr ""

#: src/Helper/Mapping.php:35
msgid "Pickup at PostNL Point"
msgstr ""

#: src/Helper/Mapping.php:40
#: src/Shipping_Method/Settings.php:595
#: src/Utils.php:596
msgid "Standard Shipment Belgium"
msgstr ""

#: src/Helper/Mapping.php:43
msgid "Pickup at PostNL Point Belgium"
msgstr ""

#: src/Helper/Mapping.php:46
#: src/Helper/Mapping.php:51
#: src/Helper/Mapping.php:52
#: src/Order/Bulk.php:511
msgid "EU Parcel"
msgstr ""

#: src/Helper/Mapping.php:47
#: src/Helper/Mapping.php:53
#: src/Order/Bulk.php:512
msgid "Non-EU Shipment"
msgstr ""

#: src/Helper/Mapping.php:50
msgid "Belgium Domestic"
msgstr ""

#: src/Main.php:369
msgid "PostNL plugin requires WooCommerce to be installed and activated!"
msgstr ""

#: src/Main.php:382
msgid "PostNL plugin requires store country to be Netherlands (NL) or Belgium (BE)!"
msgstr ""

#: src/Main.php:456
msgid "Postnl Checkout Blocks"
msgstr ""

#: src/Main.php:495
msgid "Settings"
msgstr ""

#: src/Order/Base.php:154
msgid "ID Check: "
msgstr ""

#: src/Order/Base.php:166
msgid "Insured Shipping: "
msgstr ""

#: src/Order/Base.php:178
msgid "Insured Plus: "
msgstr ""

#: src/Order/Base.php:190
msgid "Return if no answer: "
msgstr ""

#: src/Order/Base.php:202
msgid "Signature on Delivery: "
msgstr ""

#: src/Order/Base.php:214
msgid "Only Home Address: "
msgstr ""

#: src/Order/Base.php:226
msgid "Letterbox: "
msgstr ""

#: src/Order/Base.php:238
msgid "Packets: "
msgstr ""

#: src/Order/Base.php:250
msgid "Mailbox Packet (International): "
msgstr ""

#: src/Order/Base.php:262
msgid "Track & Trace: "
msgstr ""

#: src/Order/Base.php:280
#: src/Order/Bulk.php:429
msgid "Number of Labels: "
msgstr ""

#: src/Order/Base.php:311
#: src/Order/Bulk.php:448
msgid "Create Return Label: "
msgstr ""

#: src/Order/Base.php:326
#: src/Order/Bulk.php:460
msgid "Start position printing label: "
msgstr ""

#: src/Order/Base.php:330
#: src/Order/Bulk.php:466
msgid "Top Left"
msgstr ""

#: src/Order/Base.php:331
#: src/Order/Bulk.php:467
msgid "Top Right"
msgstr ""

#: src/Order/Base.php:332
#: src/Order/Bulk.php:468
msgid "Bottom Left"
msgstr ""

#: src/Order/Base.php:333
#: src/Order/Bulk.php:469
msgid "Bottom Right"
msgstr ""

#: src/Order/Base.php:464
#: src/Order/Base.php:645
#: src/Order/Single.php:509
#: src/Order/Single.php:614
#: src/Order/Single.php:687
msgid "Order does not exist!"
msgstr ""

#: src/Order/Base.php:742
msgid "Cannot create the barcode."
msgstr ""

#: src/Order/Base.php:866
msgid "Cannot create return barcode."
msgstr ""

#: src/Order/Base.php:1059
msgid "There are no files to merge."
msgstr ""

#: src/Order/Base.php:1063
msgid "\"Imagick\" must be installed on the server to merge png files."
msgstr ""

#: src/Order/Base.php:1148
#: src/Order/Base.php:1247
msgid "Cannot create the label. Label content is missing"
msgstr ""

#: src/Order/Base.php:1179
msgid "Cannot create the return label. Label content is missing"
msgstr ""

#: src/Order/Base.php:1210
msgid "Cannot create the letterbox. Label content is missing"
msgstr ""

#: src/Order/Base.php:1241
msgid "Cannot create the label. Barcode data is missing"
msgstr ""

#. translators: %s the current service.
#: src/Order/Base.php:1376
msgid "%s Tracking Number: {tracking-link}"
msgstr ""

#: src/Order/Bulk.php:89
#: src/Order/Bulk.php:641
msgid "PostNL Create Label"
msgstr ""

#: src/Order/Bulk.php:91
msgid "PostNL Change Shipping Options"
msgstr ""

#. Translators: %1$d is the order ID.
#: src/Order/Bulk.php:169
msgid "Order #%1$d already has a label."
msgstr ""

#. Translators: %1$d is the order ID.
#: src/Order/Bulk.php:179
msgid "Order #%1$d is from another shipping zone."
msgstr ""

#. translators: %1$s is anchor tag opener. %2$s is anchor tag closer.
#: src/Order/Bulk.php:267
msgid "Bulk PostNL labels file created - %1$sdownload file%2$s"
msgstr ""

#: src/Order/Bulk.php:273
msgid "Could not create bulk PostNL label file, download individually."
msgstr ""

#: src/Order/Bulk.php:412
msgid "Submit"
msgstr ""

#: src/Order/Bulk.php:505
msgid "Shipping zone"
msgstr ""

#: src/Order/Bulk.php:509
msgid "Domestic"
msgstr ""

#: src/Order/Bulk.php:510
#: src/Order/Bulk.php:528
msgid "Belgium"
msgstr ""

#: src/Order/Bulk.php:513
msgid "Pick-up at PostNL"
msgstr ""

#: src/Order/Bulk.php:519
#: src/Shipping_Method/Settings.php:566
msgid "Shipping options domestic"
msgstr ""

#: src/Order/Bulk.php:537
#: src/Shipping_Method/Settings.php:607
msgid "Shipping options EU"
msgstr ""

#: src/Order/Bulk.php:546
msgid "Shipping options non-EU"
msgstr ""

#: src/Order/Bulk.php:555
msgid "Shipping options for Pick-up at PostNL"
msgstr ""

#: src/Order/Bulk.php:635
msgid "PostNL Print Label"
msgstr ""

#. Translators: %1$s is the order ID, %2$s is the link to download the file, %3$s is the closing link tag.
#: src/Order/Bulk.php:703
msgid "#%1$s : PostNL label has been created - %2$sdownload file%3$s"
msgstr ""

#: src/Order/OrdersList.php:78
msgid "PostNL Tracking"
msgstr ""

#: src/Order/OrdersList.php:117
msgid "Delivery Date"
msgstr ""

#: src/Order/OrdersList.php:147
#: src/Utils.php:541
#: templates/checkout/postnl-delivery-day.php:75
#: build/postnl-container-frontend.js:1
#: src/Checkout_Blocks/js/postnl-delivery-day/block.js:329
msgid "As soon as possible"
msgstr ""

#: src/Order/OrdersList.php:168
msgid "Shipping options"
msgstr ""

#: src/Order/OrdersList.php:261
msgid "Fits through letterbox"
msgstr ""

#: src/Order/Single.php:129
msgid "Label & Tracking"
msgstr ""

#: src/Order/Single.php:256
msgid "Delivery Type:"
msgstr ""

#: src/Order/Single.php:287
msgid "Delivery Date:"
msgstr ""

#: src/Order/Single.php:317
msgid "Pickup Address:"
msgstr ""

#: src/Order/Single.php:354
msgid "Activate return function"
msgstr ""

#: src/Order/Single.php:357
msgid "Return function is activated for this label"
msgstr ""

#: src/Order/Single.php:360
msgid "Click here to activate the return function of this label"
msgstr ""

#: src/Order/Single.php:378
msgid "Send email with Smart Return"
msgstr ""

#: src/Order/Single.php:416
msgid "Create Shipment"
msgstr ""

#: src/Order/Single.php:418
msgid "Print Label"
msgstr ""

#: src/Order/Single.php:420
msgid "Delete Label"
msgstr ""

#: src/Order/Single.php:426
msgid "Print Return Label"
msgstr ""

#: src/Order/Single.php:431
msgid "Print Letterbox"
msgstr ""

#: src/Order/Single.php:450
#: src/Order/Single.php:495
#: src/Order/Single.php:601
#: src/Order/Single.php:674
msgid "Cannot find nonce field!"
msgstr ""

#: src/Order/Single.php:456
#: src/Order/Single.php:501
msgid "Nonce is invalid!"
msgstr ""

#: src/Order/Single.php:606
#: src/Order/Single.php:679
msgid "Nonce is invalid"
msgstr ""

#: src/Order/Single.php:618
msgid "Already activated!"
msgstr ""

#: src/Order/Single.php:634
msgid "Unknown error."
msgstr ""

#. Translators: %s is the error message.
#: src/Order/Single.php:658
msgid "Error: %s"
msgstr ""

#: src/Order/Single.php:714
msgid "PrintcodeLabel could not found"
msgstr ""

#: src/Order/Single.php:745
msgid "Email could not be send"
msgstr ""

#: src/Order/Single.php:776
msgid "18+ Adults Only"
msgstr ""

#: src/Product/Product_Editor.php:64
msgid "PostNL extra settings"
msgstr ""

#: src/Product/Product_Editor.php:65
msgid "Enable Letterbox Parcel"
msgstr ""

#: src/Product/Product_Editor.php:67
msgid "When enabled, PostNL plugin automatically determines whether a shipment fits through a letterbox."
msgstr ""

#: src/Product/Product_Editor.php:80
msgid "Maximum Quantity per Letterbox Parcel"
msgstr ""

#: src/Product/Product_Editor.php:82
#: src/Product/Single.php:111
msgid "Enter max quantity"
msgstr ""

#: src/Product/Product_Editor.php:84
msgid "Specify how many times this product fits in a letterbox parcel."
msgstr ""

#: src/Product/Product_Editor.php:95
msgid "Country of Origin"
msgstr ""

#: src/Product/Product_Editor.php:101
#: src/Product/Single.php:124
msgid "- select country -"
msgstr ""

#: src/Product/Product_Editor.php:115
msgid "Select the country of origin for this product."
msgstr ""

#: src/Product/Product_Editor.php:126
msgid "HS Tariff Code"
msgstr ""

#: src/Product/Product_Editor.php:128
#: src/Product/Single.php:135
msgid "HS Code"
msgstr ""

#: src/Product/Product_Editor.php:129
msgid "HS Tariff Code for international shipping."
msgstr ""

#. translators: %s will be replaced by service name.
#: src/Product/Single.php:100
msgid "Enable Letterbox Parcel (%s)"
msgstr ""

#. translators: %s will be replaced by service name.
#: src/Product/Single.php:101
msgid "When this setting is enabled the PostNL plug-in automatically determines whether a shipment fits through a letterbox. This choice can be overridden when creating a shipment manually via the Label & Tracking menu. This only works for orders with destination Netherlands."
msgstr ""

#. translators: %s will be replaced by service name.
#: src/Product/Single.php:108
msgid "Maximum amount per letterbox parcel (%s)"
msgstr ""

#. translators: %s will be replaced by service name.
#: src/Product/Single.php:109
msgid "Please fill in how many times this product fits in a letterbox parcel. A letterbox parcel may weigh a maximum of 2 kilograms and has the following maximum dimensions: 38x26.5x3.2 cm"
msgstr ""

#. translators: %s will be replaced by service name.
#: src/Product/Single.php:120
msgid "Country of Origin (%s)"
msgstr ""

#. translators: %s will be replaced by service name.
#: src/Product/Single.php:121
msgid "Country of Origin."
msgstr ""

#. translators: %s will be replaced by service name.
#: src/Product/Single.php:132
msgid "HS Tariff Code (%s)"
msgstr ""

#. translators: %s will be replaced by service name.
#: src/Product/Single.php:133
msgid "HS Tariff Code is a number assigned to every possible commodity that can be imported or exported from any country."
msgstr ""

#: src/Product/Single.php:222
msgid "Product ID does not exists!"
msgstr ""

#: src/Product/Single.php:281
msgid "“18+” and “Letterbox Parcel” cannot be enabled together. Letterbox has been disabled automatically."
msgstr ""

#: src/Rest_API/Barcode/Item_Info.php:66
#: src/Rest_API/Shipping/Item_Info.php:140
msgid "Order ID does not exist!"
msgstr ""

#: src/Rest_API/Barcode/Item_Info.php:136
#: src/Rest_API/Shipping/Item_Info.php:355
msgid "Customer Code is empty!"
msgstr ""

#: src/Rest_API/Barcode/Item_Info.php:139
#: src/Rest_API/Shipping/Item_Info.php:358
msgid "Customer Number is empty!"
msgstr ""

#: src/Rest_API/Base.php:273
#: src/Rest_API/Base.php:282
#: src/Rest_API/Base.php:290
msgid "Unknown error!"
msgstr ""

#: src/Rest_API/Base_Info.php:140
#: src/Rest_API/Base_Info.php:144
msgid "Shipping \"First Name\" is empty!"
msgstr ""

#: src/Rest_API/Base_Info.php:162
#: src/Rest_API/Base_Info.php:166
msgid "Shipping \"Address 1\" is empty!"
msgstr ""

#: src/Rest_API/Base_Info.php:178
msgid "Shipping \"City\" is empty!"
msgstr ""

#: src/Rest_API/Base_Info.php:181
#: src/Rest_API/Checkout/Item_Info.php:260
#: src/Rest_API/Postcode_Check/Item_Info.php:88
msgid "Shipping \"Postcode\" is empty!"
msgstr ""

#: src/Rest_API/Base_Info.php:190
msgid "Shipping \"Country\" is empty!"
msgstr ""

#: src/Rest_API/Base_Info.php:219
#: src/Rest_API/Base_Info.php:223
msgid "Store address 1 is not configured!"
msgstr ""

#: src/Rest_API/Base_Info.php:235
msgid "Store city is not configured!"
msgstr ""

#: src/Rest_API/Base_Info.php:238
msgid "Store postcode is not configured!"
msgstr ""

#: src/Rest_API/Base_Info.php:249
msgid "Store country is not configured!"
msgstr ""

#: src/Rest_API/Checkout/Item_Info.php:119
msgid "Order date is empty!"
msgstr ""

#: src/Rest_API/Checkout/Item_Info.php:123
msgid "Shipping duration is empty!"
msgstr ""

#: src/Rest_API/Checkout/Item_Info.php:131
msgid "Wrong format for cut off time!"
msgstr ""

#: src/Rest_API/Checkout/Item_Info.php:215
msgid "Locations value is not a number!"
msgstr ""

#: src/Rest_API/Postcode_Check/Item_Info.php:85
msgid "Shipping \"House number\" is empty!"
msgstr ""

#: src/Rest_API/Shipment_and_Return/Item_Info.php:82
msgid "Given id is not an Order"
msgstr ""

#: src/Rest_API/Shipment_and_Return/Item_Info.php:86
msgid "Missing barcode"
msgstr ""

#: src/Rest_API/Shipping/Item_Info.php:352
msgid "Location Code is empty!"
msgstr ""

#: src/Rest_API/Shipping/Item_Info.php:367
msgid "Store email is empty!"
msgstr ""

#: src/Rest_API/Shipping/Item_Info.php:373
msgid "Wrong format for store email!"
msgstr ""

#: src/Rest_API/Shipping/Item_Info.php:428
msgid "Order ID is empty!"
msgstr ""

#: src/Rest_API/Shipping/Item_Info.php:431
msgid "Order number is empty!"
msgstr ""

#: src/Rest_API/Shipping/Item_Info.php:434
msgid "Barcode is empty!"
msgstr ""

#: src/Rest_API/Shipping/Item_Info.php:446
msgid "Product code is empty!"
msgstr ""

#: src/Rest_API/Shipping/Item_Info.php:450
msgid "Wrong format for product code!"
msgstr ""

#: src/Rest_API/Shipping/Item_Info.php:487
msgid "Total weight is empty!"
msgstr ""

#: src/Rest_API/Shipping/Item_Info.php:502
msgid "Customer email is not valid!"
msgstr ""

#: src/Rest_API/Shipping/Item_Info.php:532
msgid "Delivery day \"Date\" is empty!"
msgstr ""

#: src/Rest_API/Shipping/Item_Info.php:548
msgid "Delivery day \"From\" is empty!"
msgstr ""

#: src/Rest_API/Shipping/Item_Info.php:561
msgid "Delivery day \"To\" is empty!"
msgstr ""

#: src/Rest_API/Shipping/Item_Info.php:577
msgid "Delivery day \"Type\" is empty!"
msgstr ""

#: src/Rest_API/Shipping/Item_Info.php:601
msgid "Pickup \"Date\" is empty!"
msgstr ""

#: src/Rest_API/Shipping/Item_Info.php:617
msgid "Pickup \"Time\" is empty!"
msgstr ""

#: src/Rest_API/Shipping/Item_Info.php:629
msgid "Pickup \"Company name\" is empty!"
msgstr ""

#: src/Rest_API/Shipping/Item_Info.php:638
msgid "Pickup \"Address 1\" is empty!"
msgstr ""

#: src/Rest_API/Shipping/Item_Info.php:653
msgid "Pickup Point \"City\" is empty!"
msgstr ""

#: src/Rest_API/Shipping/Item_Info.php:662
msgid "Pickup Point \"Postcode\" is empty!"
msgstr ""

#: src/Rest_API/Shipping/Item_Info.php:674
msgid "Pickup Point \"Country\" is empty!"
msgstr ""

#: src/Rest_API/Shipping/Item_Info.php:802
msgid "Item HS Code must be between 6 and 20 characters long"
msgstr ""

#: src/Rest_API/Shipping/Item_Info.php:815
msgid "Item \"Product ID\" is empty!"
msgstr ""

#: src/Rest_API/Shipping/Item_Info.php:818
msgid "Item \"Product SKU\" is empty!"
msgstr ""

#: src/Rest_API/Shipping/Item_Info.php:835
msgid "Item quantity must be more than 1"
msgstr ""

#: src/Rest_API/Shipping/Item_Info.php:1015
msgid "Max weight for Mailbox Packet is 2kg!"
msgstr ""

#. Translators: %s is the order total.
#: src/Rest_API/Shipping/Item_Info.php:1038
msgid "Insurance amount for EU shipments cannot exceed €5000. Your total is: %d"
msgstr ""

#: src/Shipping_Method/Fill_In_With_PostNL_Settings.php:38
#: src/Shipping_Method/Fill_In_With_PostNL_Settings.php:61
#: templates/checkout/postnl-fill-in-with-button.php:22
#: templates/checkout/postnl-fill-in-with-button.php:25
#: build/postnl-fill-in-with-frontend.js:1
#: src/Checkout_Blocks/js/postnl-fill-in-with/block.js:144
msgid "Fill in with PostNL"
msgstr ""

#: src/Shipping_Method/Fill_In_With_PostNL_Settings.php:55
msgid "With this functionality your customers can easily and automatically fill in their shipping address via their PostNL account. This functionality is only available for consumers with a Dutch shipping address. Click the following link to activate the functionality:"
msgstr ""

#: src/Shipping_Method/Fill_In_With_PostNL_Settings.php:67
#: src/Shipping_Method/Settings.php:110
#: src/Shipping_Method/Settings.php:184
#: src/Shipping_Method/Settings.php:288
#: src/Shipping_Method/Settings.php:324
#: src/Shipping_Method/Settings.php:355
#: src/Shipping_Method/Settings.php:373
#: src/Shipping_Method/Settings.php:444
#: src/Shipping_Method/Settings.php:452
msgid "Enable"
msgstr ""

#: src/Shipping_Method/Fill_In_With_PostNL_Settings.php:68
msgid "Enable Fill in with PostNL functionality."
msgstr ""

#: src/Shipping_Method/Fill_In_With_PostNL_Settings.php:74
msgid "Client ID"
msgstr ""

#: src/Shipping_Method/Fill_In_With_PostNL_Settings.php:75
msgid "Enter your PostNL Client ID from the Digital Business Portal."
msgstr ""

#: src/Shipping_Method/Fill_In_With_PostNL_Settings.php:86
msgid "Cart Button"
msgstr ""

#: src/Shipping_Method/Fill_In_With_PostNL_Settings.php:91
msgid "Automatically render button in cart page"
msgstr ""

#: src/Shipping_Method/Fill_In_With_PostNL_Settings.php:92
msgid "If enabled, the button will be automatically rendered in the cart page."
msgstr ""

#: src/Shipping_Method/Fill_In_With_PostNL_Settings.php:98
msgid "Cart page button placement"
msgstr ""

#: src/Shipping_Method/Fill_In_With_PostNL_Settings.php:103
msgid "Before Checkout"
msgstr ""

#: src/Shipping_Method/Fill_In_With_PostNL_Settings.php:104
msgid "After Checkout"
msgstr ""

#: src/Shipping_Method/Fill_In_With_PostNL_Settings.php:114
msgid "Checkout Page"
msgstr ""

#: src/Shipping_Method/Fill_In_With_PostNL_Settings.php:119
msgid "Automatically render button in checkout page"
msgstr ""

#: src/Shipping_Method/Fill_In_With_PostNL_Settings.php:120
msgid "If enabled, the button will be automatically rendered in the checkout page."
msgstr ""

#: src/Shipping_Method/Fill_In_With_PostNL_Settings.php:126
msgid "Checkout page button placement"
msgstr ""

#: src/Shipping_Method/Fill_In_With_PostNL_Settings.php:131
msgid "Before Customer Details"
msgstr ""

#: src/Shipping_Method/Fill_In_With_PostNL_Settings.php:132
msgid "After Customer Details"
msgstr ""

#: src/Shipping_Method/Fill_In_With_PostNL_Settings.php:142
msgid "Minicart"
msgstr ""

#: src/Shipping_Method/Fill_In_With_PostNL_Settings.php:147
msgid "Automatically render button in Minicart"
msgstr ""

#: src/Shipping_Method/Fill_In_With_PostNL_Settings.php:148
msgid "If enabled, the button will be automatically rendered in the minicart."
msgstr ""

#: src/Shipping_Method/Fill_In_With_PostNL_Settings.php:154
msgid "Minicart button placement"
msgstr ""

#: src/Shipping_Method/Fill_In_With_PostNL_Settings.php:159
msgid "Before Buttons"
msgstr ""

#: src/Shipping_Method/Fill_In_With_PostNL_Settings.php:160
msgid "After Buttons"
msgstr ""

#: src/Shipping_Method/Fill_In_With_PostNL_Settings.php:170
msgid "Button Styling"
msgstr ""

#: src/Shipping_Method/Fill_In_With_PostNL_Settings.php:175
msgid "Button Background Color"
msgstr ""

#: src/Shipping_Method/Fill_In_With_PostNL_Settings.php:176
msgid "Select the background color for the PostNL button."
msgstr ""

#: src/Shipping_Method/Fill_In_With_PostNL_Settings.php:184
msgid "Button Border"
msgstr ""

#: src/Shipping_Method/Fill_In_With_PostNL_Settings.php:185
msgid "Define the border style for the PostNL button (e.g., 1px solid #000000)."
msgstr ""

#: src/Shipping_Method/Fill_In_With_PostNL_Settings.php:193
msgid "Button Alignment"
msgstr ""

#: src/Shipping_Method/Fill_In_With_PostNL_Settings.php:194
msgid "Select the alignment for the PostNL button."
msgstr ""

#: src/Shipping_Method/Fill_In_With_PostNL_Settings.php:200
msgid "Left"
msgstr ""

#: src/Shipping_Method/Fill_In_With_PostNL_Settings.php:201
msgid "Center"
msgstr ""

#: src/Shipping_Method/Fill_In_With_PostNL_Settings.php:202
msgid "Right"
msgstr ""

#: src/Shipping_Method/Fill_In_With_PostNL_Settings.php:206
msgid "Button Hover Background Color"
msgstr ""

#: src/Shipping_Method/Fill_In_With_PostNL_Settings.php:207
msgid "Select the background color for the PostNL button on hover."
msgstr ""

#: src/Shipping_Method/Fill_In_With_PostNL_Settings.php:221
#: src/Shipping_Method/Fill_In_With_PostNL_Settings.php:226
msgid "Custom CSS"
msgstr ""

#: src/Shipping_Method/Fill_In_With_PostNL_Settings.php:227
msgid "Add your custom styles for the PostNL button. This will be printed in the site header."
msgstr ""

#: src/Shipping_Method/Fill_In_With_PostNL_Settings.php:239
msgid "I understand that the PostNL address fields are activated (= separate field for zipcode, housnumber, housenumber extension and street) to let consumers fill in their shipping address with the PostNL app."
msgstr ""

#: src/Shipping_Method/Fill_In_With_PostNL_Settings.php:314
msgid "You must enable at least one \"Automatically render button\" option in Cart, Checkout, or Minicart."
msgstr ""

#. translators: %1$s & %2$s is replaced with <a> tag.
#: src/Shipping_Method/PostNL.php:33
msgid "Below you will find all functions for controlling, preparing and processing your shipment with PostNL. Prerequisite is a valid PostNL business customer contract. If you are not yet a PostNL business customer, you can request a quote %1$shere%2$s."
msgstr ""

#. Translators: %s is the currency symbol.
#: src/Shipping_Method/PostNL.php:95
msgid "Free shipping from %s"
msgstr ""

#: src/Shipping_Method/PostNL.php:97
msgid "Keep empty if you don’t want to use Free shipping"
msgstr ""

#: src/Shipping_Method/Settings.php:58
msgid "Manual"
msgstr ""

#. translators: %1$s & %2$s is replaced with <a> tag.
#: src/Shipping_Method/Settings.php:61
msgid "Consult the %1$smanual%2$s for help installing the plug-in."
msgstr ""

#: src/Shipping_Method/Settings.php:65
msgid "Account Settings"
msgstr ""

#. translators: %1$s & %2$s is replaced with <a> tag.
#: src/Shipping_Method/Settings.php:68
msgid "Please configure your shipping parameters and your access towards the PostNL APIs by means of authentication. You can find the details of your PostNL account in Mijn %1$sPostNL%2$s under \"My Account > API beheren\"."
msgstr ""

#: src/Shipping_Method/Settings.php:71
msgid "Environment Mode"
msgstr ""

#: src/Shipping_Method/Settings.php:73
msgid "Choose the environment mode."
msgstr ""

#: src/Shipping_Method/Settings.php:76
msgid "Production"
msgstr ""

#: src/Shipping_Method/Settings.php:77
msgid "Sandbox"
msgstr ""

#: src/Shipping_Method/Settings.php:84
msgid "Production API Key"
msgstr ""

#. translators: %1$s & %2$s is replaced with <a> tag.
#: src/Shipping_Method/Settings.php:87
msgid "Insert your PostNL production API-key. You can find your API-key on Mijn %1$sPostNL%2$s under \"My Account\"."
msgstr ""

#: src/Shipping_Method/Settings.php:93
msgid "Sandbox API Key"
msgstr ""

#. translators: %1$s & %2$s is replaced with <a> tag.
#: src/Shipping_Method/Settings.php:96
msgid "Insert your PostNL staging API-key. You can find your API-key on Mijn %1$sPostNL%2$s under \"My Account\"."
msgstr ""

#: src/Shipping_Method/Settings.php:102
msgid "Logging"
msgstr ""

#. translators: %1$s is anchor opener tag and %2$s is anchor closer tag.
#: src/Shipping_Method/Settings.php:106
msgid "A log file containing the communication to the PostNL server will be maintained if this option is checked. This can be used in case of technical issues and can be found %1$shere%2$s."
msgstr ""

#: src/Shipping_Method/Settings.php:116
msgid "Customer Number"
msgstr ""

#: src/Shipping_Method/Settings.php:118
msgid "e.g. \"********\""
msgstr ""

#: src/Shipping_Method/Settings.php:124
msgid "Customer Code"
msgstr ""

#: src/Shipping_Method/Settings.php:126
msgid "e.g. \"DEVC\""
msgstr ""

#: src/Shipping_Method/Settings.php:133
msgid "Company Name"
msgstr ""

#: src/Shipping_Method/Settings.php:135
msgid "Enter company name - this name will be noted as the sender on the label"
msgstr ""

#: src/Shipping_Method/Settings.php:154
msgid "Return Settings"
msgstr ""

#: src/Shipping_Method/Settings.php:159
msgid "Standard return option"
msgstr ""

#: src/Shipping_Method/Settings.php:161
msgid "- None: return labels are not automatically created"
msgstr ""

#: src/Shipping_Method/Settings.php:162
msgid "- Shipment & Return: the label of the outward shipment can also be used for the return shipment."
msgstr ""

#: src/Shipping_Method/Settings.php:163
msgid "- Label in the box: a separate return label is created at the same time as the label for the outward shipment and can be included in the box."
msgstr ""

#: src/Shipping_Method/Settings.php:166
msgid "None"
msgstr ""

#: src/Shipping_Method/Settings.php:167
msgid "Shipping & Return Label"
msgstr ""

#: src/Shipping_Method/Settings.php:168
msgid "Label in the box"
msgstr ""

#: src/Shipping_Method/Settings.php:182
msgid "Directly activate return function for all labels"
msgstr ""

#: src/Shipping_Method/Settings.php:187
msgid "Yes, activate return function directly for all orders"
msgstr ""

#: src/Shipping_Method/Settings.php:188
msgid "No, activate return function per individual order"
msgstr ""

#: src/Shipping_Method/Settings.php:193
msgid "Activate Smart Return"
msgstr ""

#: src/Shipping_Method/Settings.php:196
#: src/Shipping_Method/Settings.php:202
msgid "Activate"
msgstr ""

#: src/Shipping_Method/Settings.php:200
msgid "Return to home address"
msgstr ""

#: src/Shipping_Method/Settings.php:203
msgid "[instead of business replynumber]"
msgstr ""

#: src/Shipping_Method/Settings.php:207
msgid "Replynumber"
msgstr ""

#: src/Shipping_Method/Settings.php:209
msgid "Enter replynumber."
msgstr ""

#: src/Shipping_Method/Settings.php:216
msgid "Freepost Zipcode"
msgstr ""

#: src/Shipping_Method/Settings.php:218
msgid "Enter Freepost Zipcode."
msgstr ""

#: src/Shipping_Method/Settings.php:223
msgid "Freepost City"
msgstr ""

#: src/Shipping_Method/Settings.php:225
msgid "Enter Freepost City."
msgstr ""

#: src/Shipping_Method/Settings.php:230
msgid "Street Address"
msgstr ""

#: src/Shipping_Method/Settings.php:232
msgid "Enter Return Street Address."
msgstr ""

#: src/Shipping_Method/Settings.php:237
msgid "House Number"
msgstr ""

#: src/Shipping_Method/Settings.php:239
msgid "Enter return house number."
msgstr ""

#: src/Shipping_Method/Settings.php:246
msgid "Enter return house number extension."
msgstr ""

#: src/Shipping_Method/Settings.php:251
msgid "Zipcode"
msgstr ""

#: src/Shipping_Method/Settings.php:253
msgid "Enter Return Zipcode."
msgstr ""

#: src/Shipping_Method/Settings.php:258
msgid "City"
msgstr ""

#: src/Shipping_Method/Settings.php:260
msgid "Enter Return City."
msgstr ""

#: src/Shipping_Method/Settings.php:265
msgid "Return Customer Code"
msgstr ""

#: src/Shipping_Method/Settings.php:267
msgid "Be aware that the Return Customer Code differs from the regular Customer Code. You can find your Return customer code in Mijn PostNL."
msgstr ""

#: src/Shipping_Method/Settings.php:273
msgid "Checkout Settings"
msgstr ""

#: src/Shipping_Method/Settings.php:275
msgid "Please configure your checkout preferences."
msgstr ""

#: src/Shipping_Method/Settings.php:278
msgid "Shipping Methods"
msgstr ""

#: src/Shipping_Method/Settings.php:280
msgid "Select Shipping Methods can be associated with PostNL."
msgstr ""

#: src/Shipping_Method/Settings.php:286
msgid "PostNL Pick-up Points"
msgstr ""

#: src/Shipping_Method/Settings.php:289
msgid "Show PostNL pick-up points in the checkout so that your customers can choose to get their orders delivered at a PostNL pick-up point."
msgstr ""

#: src/Shipping_Method/Settings.php:296
msgid "Extra fee pick-up delivery"
msgstr ""

#: src/Shipping_Method/Settings.php:298
msgid "Extra fee added when the customer selects a PostNL pick-up point."
msgstr ""

#: src/Shipping_Method/Settings.php:325
msgid "Show delivery days in the checkout so that your customers can choose which day to receive their order."
msgstr ""

#: src/Shipping_Method/Settings.php:332
msgid "Extra fee home delivery"
msgstr ""

#: src/Shipping_Method/Settings.php:334
msgid "Extra fee added when the customer selects home delivery."
msgstr ""

#: src/Shipping_Method/Settings.php:340
msgid "Number of Delivery Days"
msgstr ""

#: src/Shipping_Method/Settings.php:342
msgid "Number of delivery days displayed in the frontend. Maximum will be 12."
msgstr ""

#: src/Shipping_Method/Settings.php:356
msgid "Enable morning delivery in the checkout so your customers can choose to receive their orders in the morning."
msgstr ""

#: src/Shipping_Method/Settings.php:363
msgid "Morning Delivery Fee"
msgstr ""

#: src/Shipping_Method/Settings.php:365
msgid "Fee for receiving orders in the morning."
msgstr ""

#: src/Shipping_Method/Settings.php:374
msgid "Enable evening delivery in the checkout so your customers can choose to receive their orders in the evening."
msgstr ""

#: src/Shipping_Method/Settings.php:381
msgid "Evening Delivery Fee"
msgstr ""

#: src/Shipping_Method/Settings.php:383
msgid "Fee for receiving orders in the evening."
msgstr ""

#: src/Shipping_Method/Settings.php:389
msgid "Transit Time"
msgstr ""

#: src/Shipping_Method/Settings.php:391
msgid "The number of days it takes for the order to be delivered after the order has been placed."
msgstr ""

#: src/Shipping_Method/Settings.php:397
msgid "Cut Off Time"
msgstr ""

#: src/Shipping_Method/Settings.php:399
msgid "If an order is ordered after this time, one day will be added to the transit time."
msgstr ""

#: src/Shipping_Method/Settings.php:405
msgid "Drop off Days"
msgstr ""

#: src/Shipping_Method/Settings.php:407
#: src/Utils.php:32
msgid "Monday"
msgstr ""

#: src/Shipping_Method/Settings.php:408
msgid "Select which days orders will be shipped."
msgstr ""

#: src/Shipping_Method/Settings.php:414
#: src/Utils.php:33
msgid "Tuesday"
msgstr ""

#: src/Shipping_Method/Settings.php:419
#: src/Utils.php:34
msgid "Wednesday"
msgstr ""

#: src/Shipping_Method/Settings.php:424
#: src/Utils.php:35
msgid "Thursday"
msgstr ""

#: src/Shipping_Method/Settings.php:429
#: src/Utils.php:36
msgid "Friday"
msgstr ""

#: src/Shipping_Method/Settings.php:434
#: src/Utils.php:37
msgid "Saturday"
msgstr ""

#: src/Shipping_Method/Settings.php:439
#: src/Utils.php:38
msgid "Sunday"
msgstr ""

#: src/Shipping_Method/Settings.php:442
msgid "Validate Dutch addresses"
msgstr ""

#: src/Shipping_Method/Settings.php:445
msgid "Based on zipcode and housenumber combination the address is checked."
msgstr ""

#: src/Shipping_Method/Settings.php:450
msgid "Use PostNL address-field"
msgstr ""

#: src/Shipping_Method/Settings.php:453
msgid "For zipcode, housenumber, housenumber extension and street separate address fields are displayed when this settings is enabled. This only applies for Dutch addresses."
msgstr ""

#: src/Shipping_Method/Settings.php:460
msgid "Shipping Outside Europe Settings"
msgstr ""

#: src/Shipping_Method/Settings.php:462
msgid "Please insert your Globalpack credentials."
msgstr ""

#: src/Shipping_Method/Settings.php:465
msgid "GlobalPack Barcode Type"
msgstr ""

#: src/Shipping_Method/Settings.php:470
msgid "CD"
msgstr ""

#: src/Shipping_Method/Settings.php:474
msgid "GlobalPack Customer Code"
msgstr ""

#: src/Shipping_Method/Settings.php:479
msgid "1234"
msgstr ""

#: src/Shipping_Method/Settings.php:483
msgid "Default HS Tariff Code"
msgstr ""

#: src/Shipping_Method/Settings.php:485
msgid "The HS tariff code is used by customs to classify goods. The HS tariff code can be found on the website of the Dutch Chamber of Commerce."
msgstr ""

#: src/Shipping_Method/Settings.php:491
msgid "Default Country of Origin"
msgstr ""

#: src/Shipping_Method/Settings.php:493
msgid "Default country of origin is used by customs."
msgstr ""

#: src/Shipping_Method/Settings.php:502
msgid "Printer &amp; Email Settings"
msgstr ""

#: src/Shipping_Method/Settings.php:504
msgid "Please configure your printer and email preferences."
msgstr ""

#: src/Shipping_Method/Settings.php:507
msgid "Printer Type"
msgstr ""

#: src/Shipping_Method/Settings.php:509
msgid "It is not recommended to send .pdf files/labels directly to a Zebra printer If you want to send it directly to your Zebra printer, please use .gif files or the native (generic) zpl printer type"
msgstr ""

#: src/Shipping_Method/Settings.php:520
msgid "DPI"
msgstr ""

#: src/Shipping_Method/Settings.php:530
msgid "Label Format"
msgstr ""

#: src/Shipping_Method/Settings.php:532
msgid "Use A6 format in case you use a labelprinter. Use A4 format for other regular printers."
msgstr ""

#: src/Shipping_Method/Settings.php:542
msgid "WooCommerce Email"
msgstr ""

#: src/Shipping_Method/Settings.php:544
#: src/Shipping_Method/Settings.php:545
msgid "When PostNL label is created send email to customer."
msgstr ""

#: src/Shipping_Method/Settings.php:551
msgid "WooCommerce Email Text"
msgstr ""

#: src/Shipping_Method/Settings.php:553
msgid "Text added for tracking note email."
msgstr ""

#: src/Shipping_Method/Settings.php:555
#: src/Shipping_Method/Settings.php:556
msgid "This is your track and track link {tracking-link}"
msgstr ""

#: src/Shipping_Method/Settings.php:560
#: src/Shipping_Method/Settings.php:678
msgid "Default shipping Options Settings"
msgstr ""

#: src/Shipping_Method/Settings.php:562
#: src/Shipping_Method/Settings.php:680
msgid "Please select Default shipping Options."
msgstr ""

#: src/Shipping_Method/Settings.php:568
msgid "Select a default shipping option for domestic orders that are shipped with PostNL."
msgstr ""

#: src/Shipping_Method/Settings.php:572
#: src/Utils.php:589
msgid "Standard shipment"
msgstr ""

#: src/Shipping_Method/Settings.php:573
#: src/Shipping_Method/Settings.php:648
#: src/Utils.php:590
msgid "ID Check"
msgstr ""

#: src/Shipping_Method/Settings.php:574
msgid "ID Check + Insured Shipping"
msgstr ""

#: src/Shipping_Method/Settings.php:576
#: src/Utils.php:591
msgid "Return if no answer"
msgstr ""

#: src/Shipping_Method/Settings.php:577
#: src/Utils.php:592
msgid "Signature on Delivery"
msgstr ""

#: src/Shipping_Method/Settings.php:578
#: src/Utils.php:593
msgid "Only Home Address"
msgstr ""

#: src/Shipping_Method/Settings.php:579
#: src/Utils.php:594
msgid "Letterbox"
msgstr ""

#: src/Shipping_Method/Settings.php:580
msgid "Signature on Delivery + Insured Shipping"
msgstr ""

#: src/Shipping_Method/Settings.php:581
msgid "Signature on Delivery + Return if no answer"
msgstr ""

#: src/Shipping_Method/Settings.php:582
msgid "Insured Shipping + Return if no answer + Signature on Delivery"
msgstr ""

#: src/Shipping_Method/Settings.php:583
msgid "Only Home Address + Return if no answer"
msgstr ""

#: src/Shipping_Method/Settings.php:584
msgid "Only Home Address + Return if no answer + Signature on Delivery"
msgstr ""

#: src/Shipping_Method/Settings.php:585
msgid "Only Home Address + Signature on Delivery"
msgstr ""

#: src/Shipping_Method/Settings.php:589
msgid "Shipping options Belgium"
msgstr ""

#: src/Shipping_Method/Settings.php:591
msgid "Select a default shipping option for the orders shipped to Belgium with PostNL."
msgstr ""

#: src/Shipping_Method/Settings.php:596
msgid "Standard Shipment Belgium + Only Home Address"
msgstr ""

#: src/Shipping_Method/Settings.php:597
msgid "Standard Shipment Belgium + Signature on Delivery"
msgstr ""

#: src/Shipping_Method/Settings.php:598
msgid "Standard Shipment Belgium + Insured Shipping"
msgstr ""

#: src/Shipping_Method/Settings.php:599
#: src/Shipping_Method/Settings.php:616
#: src/Shipping_Method/Settings.php:633
#: src/Utils.php:597
msgid "Boxable Packet"
msgstr ""

#: src/Shipping_Method/Settings.php:600
#: src/Shipping_Method/Settings.php:617
#: src/Shipping_Method/Settings.php:634
msgid "Boxable Packet + Track & Trace"
msgstr ""

#: src/Shipping_Method/Settings.php:601
#: src/Shipping_Method/Settings.php:618
#: src/Shipping_Method/Settings.php:635
msgid "Packets"
msgstr ""

#: src/Shipping_Method/Settings.php:602
#: src/Shipping_Method/Settings.php:619
#: src/Shipping_Method/Settings.php:636
msgid "Packets + Track & Trace"
msgstr ""

#: src/Shipping_Method/Settings.php:603
#: src/Shipping_Method/Settings.php:620
#: src/Shipping_Method/Settings.php:637
msgid "Packets + Track & Trace + Insured"
msgstr ""

#: src/Shipping_Method/Settings.php:609
msgid "Select a default shipping option for the orders shipped within European Union zone."
msgstr ""

#: src/Shipping_Method/Settings.php:613
msgid "EU Parcel + Track & Trace"
msgstr ""

#: src/Shipping_Method/Settings.php:614
msgid "EU Parcel + Track & Trace + Insured"
msgstr ""

#: src/Shipping_Method/Settings.php:615
msgid "EU Parcel + Track & Trace + Insured Plus"
msgstr ""

#: src/Shipping_Method/Settings.php:624
#: src/Shipping_Method/Settings.php:686
msgid "Default Shipping International"
msgstr ""

#: src/Shipping_Method/Settings.php:626
msgid "Shipping options non-EU (outside the EU borders)."
msgstr ""

#: src/Shipping_Method/Settings.php:630
msgid "Parcel non-EU + Track & Trace"
msgstr ""

#: src/Shipping_Method/Settings.php:631
msgid "Parcel non-EU + Track & Trace + Insured"
msgstr ""

#: src/Shipping_Method/Settings.php:632
#: src/Shipping_Method/Settings.php:692
msgid "Parcel non-EU + Track & Trace + Insured Plus"
msgstr ""

#: src/Shipping_Method/Settings.php:641
msgid "Default Shipping Pickup"
msgstr ""

#: src/Shipping_Method/Settings.php:643
msgid "Shipping options Pickup."
msgstr ""

#: src/Shipping_Method/Settings.php:647
msgid "Standard Shipping"
msgstr ""

#: src/Shipping_Method/Settings.php:649
#: src/Utils.php:599
msgid "Insured Shipping"
msgstr ""

#: src/Shipping_Method/Settings.php:653
msgid "Automatically change order status to Completed"
msgstr ""

#: src/Shipping_Method/Settings.php:655
#: src/Shipping_Method/Settings.php:656
msgid "Automatically change order status to Completed once an order has been pre-alerted and printed"
msgstr ""

#: src/Shipping_Method/Settings.php:688
msgid "Select a default shipping option for the orders shipped internationally (outside the EU borders)."
msgstr ""

#. translators: %s is a field argument.
#: src/Utils.php:295
msgid "Please specify a \"%s\" argument"
msgstr ""

#: src/Utils.php:595
msgid "Packet"
msgstr ""

#: src/Utils.php:598
msgid "Track & Trace"
msgstr ""

#: src/Utils.php:607
msgid "Parcels Non-EU Insured"
msgstr ""

#: src/Utils.php:608
msgid "Parcels non-EU Insured Plus"
msgstr ""

#: src/Utils.php:614
msgid "Parcels EU"
msgstr ""

#: src/Utils.php:615
#: src/Utils.php:622
msgid "Insured Plus"
msgstr ""

#: src/Utils.php:621
msgid "Parcels Non-EU"
msgstr ""

#: templates/checkout/postnl-container.php:21
#: build/postnl-container-frontend.js:1
#: src/Checkout_Blocks/js/postnl-container/block.js:486
msgid "These items are eligible for letterbox delivery."
msgstr ""

#: templates/checkout/postnl-delivery-day.php:44
#: build/postnl-container-frontend.js:1
#: src/Checkout_Blocks/js/postnl-delivery-day/block.js:265
msgid "Evening"
msgstr ""

#: templates/checkout/postnl-delivery-day.php:46
#: build/postnl-container-frontend.js:1
#: src/Checkout_Blocks/js/postnl-delivery-day/block.js:273
msgid "Morning"
msgstr ""

#. translators: %1$s is <strong> opener and %2$s is <strong> closer.
#: templates/checkout/postnl-dropoff-points.php:103
#: build/postnl-container-frontend.js:1
#: src/Checkout_Blocks/js/postnl-dropoff-points/block.js:464
msgid "Receive shipment at home? Make a selection from the Delivery Days."
msgstr ""

#: templates/checkout/postnl-dropoff-points.php:137
msgid "Vanaf"
msgstr ""

#: templates/checkout/postnl-fill-in-with-button.php:23
#: build/postnl-fill-in-with-frontend.js:1
#: src/Checkout_Blocks/js/postnl-fill-in-with/block.js:160
msgid "PostNL Logo"
msgstr ""

#: templates/checkout/postnl-fill-in-with-button.php:29
#: build/postnl-fill-in-with-frontend.js:1
#: src/Checkout_Blocks/js/postnl-fill-in-with/block.js:145
msgid "Your name and address are automatically filled in via your PostNL account. That saves you from having to fill in the form!"
msgstr ""

#. translators: %s: Order number
#: templates/emails/plain/smart-return-email.php:27
#: templates/emails/smart-return-email.php:30
msgid "In this email you will find the barcode you need to return your order. Scan this barcode at a PostNL point to print your return label. Please note, wait at least 10 minutes before scanning the barcode after receipt of this mail."
msgstr ""

#: build/postnl-fill-in-with-frontend.js:1
#: src/Checkout_Blocks/js/postnl-fill-in-with/block.js:75
msgid "Failed to retrieve PostNL user data."
msgstr ""

#: build/postnl-fill-in-with-frontend.js:1
#: src/Checkout_Blocks/js/postnl-fill-in-with/block.js:83
msgid "Failed to retrieve PostNL address. Please try again."
msgstr ""

#: build/postnl-fill-in-with-frontend.js:1
#: src/Checkout_Blocks/js/postnl-fill-in-with/block.js:109
msgid "Failed to initiate PostNL login."
msgstr ""

#: build/postnl-fill-in-with-frontend.js:1
#: src/Checkout_Blocks/js/postnl-fill-in-with/block.js:121
msgid "An unknown error occurred."
msgstr ""

#: src/Checkout_Blocks/js/postnl-delivery-day/edit.js:12
msgid "PostNL dilvery day Options"
msgstr ""

#: src/Checkout_Blocks/js/postnl-dropoff-points/edit.js:12
msgid "PostNL Dropof point Options"
msgstr ""

#: build/Checkout_Blocks/js/postnl-container/block.json
#: src/Checkout_Blocks/js/postnl-container/block.json
msgctxt "block title"
msgid "Postnl Tabs"
msgstr ""

#: build/Checkout_Blocks/js/postnl-fill-in-with/block.json
#: src/Checkout_Blocks/js/postnl-fill-in-with/block.json
msgctxt "block title"
msgid "PostNL Fill Button"
msgstr ""

#: build/Checkout_Blocks/js/postnl-fill-in-with/block.json
#: src/Checkout_Blocks/js/postnl-fill-in-with/block.json
msgctxt "block description"
msgid "Fill in address with PostNL account"
msgstr ""
