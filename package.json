{"name": "postnl-for-woocommerce", "version": "5.8.0", "author": "The WordPress Contributors", "license": "GPL-2.0-or-later", "main": "build/index.js", "scripts": {"build": "composer install && wp-scripts build && npm run makepot && composer install --no-dev && npm run archive", "format": "wp-scripts format", "lint:css": "wp-scripts lint-style", "lint": "wp-scripts lint-js ./src", "lint:fix": "wp-scripts lint-js ./src --fix", "packages-update": "wp-scripts packages-update", "plugin-zip": "wp-scripts plugin-zip", "start": "wp-scripts start", "env": "wp-env", "makepot": "vendor/bin/wp i18n make-pot ./ languages/$npm_package_name.pot --exclude=node_modules,tests,docs,vendor", "makepot-json": "vendor/bin/wp i18n make-json languages/$npm_package_name-nl_NL.po --no-purge", "archive": "composer archive --file=$npm_package_config_archiveName --format=zip", "postarchive": "rm -rf $npm_package_config_archiveName && unzip $npm_package_config_archiveName.zip -d $npm_package_config_archiveName && rm $npm_package_config_archiveName.zip && zip -r $npm_package_config_archiveName.zip $npm_package_config_archiveName && rm -rf $npm_package_config_archiveName"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.0.0", "@woocommerce/dependency-extraction-webpack-plugin": "^3.1.0", "@woocommerce/eslint-plugin": "^2.2.0", "@wordpress/browserslist-config": "^6.10.0", "@wordpress/data": "^10.10.0", "@wordpress/dependency-extraction-webpack-plugin": "^6.10.0", "@wordpress/env": "^7.0.0", "@wordpress/prettier-config": "^2.18.0", "@wordpress/scripts": "^30.2.0", "eslint": "^8.56.0", "eslint-import-resolver-webpack": "^0.13.10", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsdoc": "^50.6.3", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^5.1.0", "mini-css-extract-plugin": "^2.5.3", "prettier": "npm:wp-prettier@^2.6.2"}, "dependencies": {"@woocommerce/date": "^4.2.0", "@wordpress/block-editor": "^14.15.0", "@wordpress/blocks": "^13.10.0", "@wordpress/i18n": "^5.20.0", "@wordpress/icons": "^10.10.0", "@wordpress/plugins": "^7.20.0", "lodash": "^4.17.21"}, "config": {"archiveName": "woo-postnl"}}